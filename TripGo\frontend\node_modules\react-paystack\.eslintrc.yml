env:
  browser: true
  es6: true
  node: true
extends:
  - "eslint:recommended"
  - "plugin:@typescript-eslint/eslint-recommended"
  - "plugin:@typescript-eslint/recommended"
  - "plugin:prettier/recommended"
  - "plugin:jest/recommended"
  - "prettier"
  - "plugin:react/recommended"
  - "plugin:react/recommended"
globals:
  Atomics: readonly
  SharedArrayBuffer: readonly
parser: "@typescript-eslint/parser"
parserOptions:
  ecmaFeatures:
    jsx: true
  ecmaVersion: 2018
  sourceType: module
plugins:
  - react
  - "@typescript-eslint"
  - prettier
  - jest
rules: {
  "@typescript-eslint/no-explicit-any": "off",
  "@typescript-eslint/camelcase": "off",
  "@typescript-eslint/no-unused-vars": "off",
  "@typescript-eslint/ban-ts-ignore": "off",
  "@typescript-eslint/interface-name-prefix": "off",
  "@typescript-eslint/ban-ts-comment": "off",
}
