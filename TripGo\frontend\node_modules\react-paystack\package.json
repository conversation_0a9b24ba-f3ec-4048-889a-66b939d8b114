{"name": "react-paystack", "version": "5.0.0", "description": "This is an reactJS library for implementing paystack payment gateway", "main": "dist/index.js", "module": "dist/index.es.js", "typings": "dist/index.d.ts", "jsnext:main": "dist/index.es.js", "scripts": {"build": "rm -rf dist && rollup -c --bundleConfigAsCjs", "build:watch": "rm -rf dist && rollup -c --watch --bundleConfigAsCjs", "format": "prettier --write '**/**/*.{js,}'", "lint": "eslint --ext .js,.ts,.tsx,.jsx --ignore-path .eslintignore .", "lint:fix": "npm run lint -- --fix", "test": "jest --env=jsdom", "test:watch": "jest --watch --verbose --env=jsdom"}, "repository": {"type": "git", "url": "git+https://github.com/iamraphson/react-paystack.git"}, "keywords": ["Javascript", "github", "ReactJS", "Open Source", "payments", "paystack", "Gateway"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/iamraphson/react-paystack/issues"}, "homepage": "https://github.com/iamraphson/react-paystack#readme", "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/preset-env": "^7.21.4", "@babel/preset-typescript": "^7.21.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-typescript": "^11.0.0", "@testing-library/react": "^14.0.0", "@testing-library/react-hooks": "^8.0.1", "@types/jest": "^29.5.0", "@types/react": "^18.0.32", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "babel-eslint": "^10.1.0", "babel-jest": "^29.5.0", "eslint": "^8.37.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react": "^7.32.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "^3.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-test-renderer": "^18.2.0", "rollup": "^4.9.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-typescript2": "^0.36.0", "ts-jest": "^29.1.0", "typescript": "^5.0.3"}}