import React, { useState, useEffect, useContext } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { AppContext } from "../context/AppContext";
import { motion } from "framer-motion";
import { usePaystackPayment } from "react-paystack";

const Booking = () => {
  const { user, token, backendUrl } = useContext(AppContext);
  const location = useLocation();
  const navigate = useNavigate();
  const tour = location.state?.tour;

  // Redirect to login if user is not authenticated
  if (!tour || !user || !token) {
    navigate("/login");
    return null;
  }

  const { title, price } = tour;

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    travelers: 1,
    specialRequests: "",
  });
  const [totalPrice, setTotalPrice] = useState(price);
  const [isProcessing, setIsProcessing] = useState(false);
  const [bookingId, setBookingId] = useState(null);

  // Paystack configuration
  const config = {
    reference: bookingId ? `booking_${bookingId}_${Date.now()}` : '',
    email: formData.email,
    amount: Math.round(totalPrice * 100), // Amount in pesewas (GHS smallest unit)
    currency: 'GHS',
    publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || 'pk_test_your_paystack_public_key_here',
    metadata: {
      bookingId: bookingId,
      tourTitle: tour?.title,
      customerName: formData.name,
    },
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  // Paystack payment success handler
  const onSuccess = async (reference) => {
    try {
      console.log("Payment success callback triggered with reference:", reference);
      setIsProcessing(true);

      // Verify payment with backend
      const response = await fetch(`${backendUrl}/api/payments/verify/${reference.reference}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      const data = await response.json();
      console.log("Payment verification response:", data);

      if (data.success) {
        const booking = data.booking;

        // Show success message with booking details
        toast.success(
          `🎉 Payment successful! Your booking for "${booking.tourTitle}" has been received.
          Total: GH₵${booking.totalPrice} | Payment: ${booking.paymentStatus.toUpperCase()}`,
          { autoClose: 5000 }
        );

        // Show additional info toast with navigation options
        toast.info(
          "Your payment has been received and is awaiting admin confirmation. You can view your booking details in 'My Bookings' section. Redirecting to tours page...",
          { autoClose: 6000 }
        );

        // Reset processing state before navigation
        setIsProcessing(false);

        // Redirect to tours page after successful payment
        setTimeout(() => {
          navigate("/tours");
        }, 5000); // Give user time to see the success messages
      } else {
        console.error("Payment verification failed:", data);
        toast.error("Payment verification failed. Please contact support.");
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Payment verification error:", error);
      toast.error("Error verifying payment. Please contact support.");
      setIsProcessing(false);
    }
  };

  // Paystack payment close handler
  const onClose = () => {
    setIsProcessing(false); // Reset processing state when payment is cancelled
    toast.info("Payment cancelled. You can try again anytime.");
  };

  // Initialize Paystack payment hook
  const initializePayment = usePaystackPayment(config);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.name || !formData.email || !formData.phone) {
      toast.error("Please fill out all required fields.");
      return;
    }

    if (isProcessing) {
      return;
    }

    try {
      setIsProcessing(true);

      // First create the booking
      const response = await fetch(
        `${backendUrl}/api/bookings`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`,
          },
          body: JSON.stringify({
            ...formData,
            tourId: tour.id || tour._id,
            tourTitle: tour.title,
            totalPrice,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create booking");
      }

      const data = await response.json();
      console.log("Booking created successfully:", data);
      setBookingId(data.booking._id);

      // Initialize payment with Paystack
      console.log("Initializing payment with config:", config);
      initializePayment(onSuccess, onClose);

      // Reset processing state after payment initialization
      // The onSuccess/onClose handlers will manage the state during payment flow
      setIsProcessing(false);

    } catch (error) {
      console.error("Booking error:", error);
      toast.error("Error: " + error.message);
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    setTotalPrice(price * parseInt(formData.travelers, 10));
  }, [formData.travelers, price, tour]);

  const formVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  return user ? (
    <motion.div
      className="max-w-4xl mx-auto p-6 bg-white/20 rounded-lg  shadow-lg"
      variants={formVariants}
      initial="hidden"
      animate="visible"
    >
      <h2 className="text-3xl font-bold mb-6">Book Your Tour: {title}</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-lg font-semibold">Name</label>
          <motion.input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="w-full p-3 border rounded-lg bg-inherit"
            required
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />
        </div>
        <div>
          <label className="block text-lg font-semibold">Email</label>
          <motion.input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className="w-full p-3 border rounded-lg bg-inherit"
            required
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />
        </div>
        <div>
          <label className="block text-lg font-semibold">Phone Number</label>
          <motion.input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            className="w-full p-3 border rounded-lg bg-inherit"
            required
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />
        </div>
        <div>
          <label className="block text-lg font-semibold">
            Number of Travelers
          </label>
          <motion.input
            type="number"
            name="travelers"
            min="1"
            value={formData.travelers}
            onChange={handleChange}
            className="w-full p-3 border rounded-lg bg-inherit"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />
        </div>
        <div>
          <label className="block text-lg font-semibold">
            Special Requests (Optional)
          </label>
          <motion.textarea
            name="specialRequests"
            value={formData.specialRequests}
            onChange={handleChange}
            className="w-full p-3 border rounded-lg bg-inherit"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />
        </div>
        <div>
          <h3 className="text-lg font-semibold">Total Price: GH₵{totalPrice}</h3>
        </div>
        <motion.button
          type="submit"
          disabled={isProcessing || !formData.email}
          className={`w-full p-3 rounded-lg transition duration-300 ${
            isProcessing || !formData.email
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-gradient-to-b from-sky-500 to-blue-500 text-white hover:from-sky-800 hover:to-blue-700"
          }`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {isProcessing ? "Processing..." : "Pay Now with Paystack"}
        </motion.button>
      </form>
    </motion.div>
  ) : (
    <div className="text-center mt-20">Please log in to make a booking.</div>
  );
};

export default Booking;
