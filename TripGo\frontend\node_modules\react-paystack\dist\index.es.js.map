{"version": 3, "file": "index.es.js", "sources": ["../libs/paystack-script.ts", "../libs/paystack-actions.ts", "../libs/use-paystack.ts", "../libs/paystack-button.tsx", "../libs/paystack-context.ts", "../libs/paystack-provider.tsx", "../libs/paystack-consumer.tsx"], "sourcesContent": ["import {useState, useEffect} from 'react';\n\nconst cachedScripts: string[] = [];\ninterface IScriptResult {\n  loaded: boolean;\n  error: boolean;\n}\n\nexport default function usePaystackScript(): boolean[] {\n  const src = 'https://js.paystack.co/v1/inline.js';\n\n  const [state, setState] = useState<IScriptResult>({\n    loaded: false,\n    error: false,\n  });\n\n  useEffect((): any => {\n    if (cachedScripts.includes(src)) {\n      setState({\n        loaded: true,\n        error: false,\n      });\n    } else {\n      cachedScripts.push(src);\n\n      const script = document.createElement('script');\n      script.src = src;\n      script.async = true;\n\n      const onScriptLoad = (): void => {\n        setState({\n          loaded: true,\n          error: false,\n        });\n      };\n\n      const onScriptError = (): void => {\n        const index = cachedScripts.indexOf(src);\n        if (index >= 0) cachedScripts.splice(index, 1);\n        script.remove();\n\n        setState({\n          loaded: true,\n          error: true,\n        });\n      };\n\n      script.addEventListener('load', onScriptLoad);\n      script.addEventListener('complete', onScriptLoad);\n      script.addEventListener('error', onScriptError);\n\n      document.body.appendChild(script);\n\n      return (): void => {\n        script.removeEventListener('load', onScriptLoad);\n        script.removeEventListener('error', onScriptError);\n      };\n    }\n  }, [src]);\n\n  return [state.loaded, state.error];\n}\n", "/* eslint-disable */\nexport let callPaystackPop = (paystackArgs: Record<string, any>): void => {\n  // @ts-ignore\n  const handler = window.PaystackPop && window.PaystackPop.setup(paystackArgs);\n  handler && handler.openIframe();\n};\n", "import {useEffect} from 'react';\nimport {HookConfig, InitializePayment} from './types';\nimport usePaystackScript from './paystack-script';\nimport {callPaystackPop} from './paystack-actions';\n\nexport default function usePaystackPayment(hookConfig: HookConfig): InitializePayment {\n  const [scriptLoaded, scriptError] = usePaystackScript();\n\n  function initializePayment({config, onSuccess, onClose}: Parameters<InitializePayment>[0]): void {\n    if (scriptError) {\n      throw new Error('Unable to load paystack inline script');\n    }\n\n    const args = {...hookConfig, ...config};\n\n    const {\n      publicKey,\n      firstname,\n      lastname,\n      phone,\n      email,\n      amount,\n      reference,\n      metadata = {},\n      currency = 'NGN',\n      channels,\n      label = '',\n      plan = '',\n      quantity = '',\n      subaccount = '',\n      transaction_charge = 0,\n      bearer = 'account',\n      split,\n      split_code,\n    } = args;\n\n    if (scriptLoaded) {\n      const paystackArgs: Record<string, any> = {\n        callback: onSuccess ? onSuccess : () => null,\n        onClose: onClose ? onClose : () => null,\n        key: publicKey,\n        ref: reference,\n        email,\n        firstname,\n        lastname,\n        phone,\n        amount,\n        currency,\n        plan,\n        quantity,\n        channels,\n        subaccount,\n        transaction_charge,\n        bearer,\n        label,\n        metadata,\n        split,\n        split_code,\n        'data-custom-button': args['data-custom-button'] || '',\n      };\n      callPaystackPop(paystackArgs);\n    }\n  }\n\n  useEffect(() => {\n    if (scriptError) {\n      throw new Error('Unable to load paystack inline script');\n    }\n  }, [scriptError]);\n\n  return initializePayment;\n}\n", "import React, {ReactNode} from 'react';\nimport usePaystackPayment from './use-paystack';\nimport {callback, PaystackProps} from './types';\n\ninterface PaystackButtonProps extends PaystackProps {\n  text?: string;\n  className?: string;\n  children?: ReactNode;\n  onSuccess?: callback;\n  onClose?: callback;\n}\n\nconst PaystackButton = ({\n  text,\n  className,\n  children,\n  onSuccess,\n  onClose,\n  ...config\n}: PaystackButtonProps): JSX.Element => {\n  const initializePayment = usePaystackPayment(config);\n\n  return (\n    <button\n      className={className}\n      onClick={(): void => initializePayment({config, onSuccess, onClose})}\n    >\n      {text || children}\n    </button>\n  );\n};\n\nexport default PaystackButton;\n", "import {createContext} from 'react';\nimport {InitializePayment, PaystackProps} from './types';\n\ntype IPaystackContext = {\n  config: PaystackProps;\n  initializePayment: InitializePayment;\n  onSuccess: () => void;\n  onClose: () => void;\n};\n\nconst PaystackContext = createContext<IPaystackContext>({\n  config: {} as PaystackProps,\n  initializePayment: () => null,\n  onSuccess: () => null,\n  onClose: () => null,\n});\n\nexport default PaystackContext;\n", "import React from 'react';\nimport PaystackContext from './paystack-context';\nimport usePaystackPayment from './use-paystack';\nimport {callback, PaystackProps} from './types';\n\ninterface PaystackProviderProps extends PaystackProps {\n  children: JSX.Element;\n  onSuccess: callback;\n  onClose: callback;\n}\n\nconst PaystackProvider = ({\n  children,\n  onSuccess,\n  onClose,\n  ...config\n}: PaystackProviderProps): JSX.Element => {\n  const initializePayment = usePaystackPayment(config);\n\n  return (\n    <PaystackContext.Provider value={{config, initializePayment, onSuccess, onClose}}>\n      {children}\n    </PaystackContext.Provider>\n  );\n};\n\nexport default PaystackProvider;\n", "import React, {forwardRef, useContext, FunctionComponentElement} from 'react';\nimport PaystackProvider from './paystack-provider';\nimport {PaystackProps} from './types';\nimport PaystackContext from './paystack-context';\n\ninterface PaystackConsumerProps extends PaystackProps {\n  children: (arg: Record<string, any>) => any;\n  onSuccess?: () => void;\n  onClose?: () => void;\n}\n\nconst PaystackConsumerChild = ({\n  children,\n  ref,\n}: {\n  children: any;\n  ref: any;\n}): FunctionComponentElement<any> => {\n  const {config, initializePayment, onSuccess, onClose} = useContext(PaystackContext);\n\n  const completeInitializePayment = (): void => initializePayment({config, onSuccess, onClose});\n  return children({initializePayment: completeInitializePayment, ref});\n};\n\n// eslint-disable-next-line react/display-name\nconst PaystackConsumer = forwardRef(\n  (\n    {children, onSuccess: paraSuccess, onClose: paraClose, ...others}: PaystackConsumerProps,\n    ref: any,\n  ): JSX.Element => {\n    const onSuccess = paraSuccess ? paraSuccess : (): any => null;\n    const onClose = paraClose ? paraClose : (): any => null;\n    return (\n      <PaystackProvider {...others} onSuccess={onSuccess} onClose={onClose}>\n        <PaystackConsumerChild ref={ref}>{children}</PaystackConsumerChild>\n      </PaystackProvider>\n    );\n  },\n);\n\nexport default PaystackConsumer;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,aAAa,GAAa,EAAE,CAAC;AAMrB,SAAU,iBAAiB,GAAA;IACvC,IAAM,GAAG,GAAG,qCAAqC,CAAC;IAE5C,IAAA,EAAA,GAAoB,QAAQ,CAAgB;AAChD,QAAA,MAAM,EAAE,KAAK;AACb,QAAA,KAAK,EAAE,KAAK;AACb,KAAA,CAAC,EAHK,KAAK,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,QAAQ,QAGpB,CAAC;AAEH,IAAA,SAAS,CAAC,YAAA;AACR,QAAA,IAAI,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC/B,YAAA,QAAQ,CAAC;AACP,gBAAA,MAAM,EAAE,IAAI;AACZ,gBAAA,KAAK,EAAE,KAAK;AACb,aAAA,CAAC,CAAC;SACJ;aAAM;AACL,YAAA,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAExB,IAAM,QAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChD,YAAA,QAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB,YAAA,QAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AAEpB,YAAA,IAAM,cAAY,GAAG,YAAA;AACnB,gBAAA,QAAQ,CAAC;AACP,oBAAA,MAAM,EAAE,IAAI;AACZ,oBAAA,KAAK,EAAE,KAAK;AACb,iBAAA,CAAC,CAAC;AACL,aAAC,CAAC;AAEF,YAAA,IAAM,eAAa,GAAG,YAAA;gBACpB,IAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,KAAK,IAAI,CAAC;AAAE,oBAAA,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC/C,QAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,gBAAA,QAAQ,CAAC;AACP,oBAAA,MAAM,EAAE,IAAI;AACZ,oBAAA,KAAK,EAAE,IAAI;AACZ,iBAAA,CAAC,CAAC;AACL,aAAC,CAAC;AAEF,YAAA,QAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,cAAY,CAAC,CAAC;AAC9C,YAAA,QAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,cAAY,CAAC,CAAC;AAClD,YAAA,QAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,eAAa,CAAC,CAAC;AAEhD,YAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAM,CAAC,CAAC;YAElC,OAAO,YAAA;AACL,gBAAA,QAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,cAAY,CAAC,CAAC;AACjD,gBAAA,QAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,eAAa,CAAC,CAAC;AACrD,aAAC,CAAC;SACH;AACH,KAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAEV,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACrC;;AC7DA;AACO,IAAI,eAAe,GAAG,UAAC,YAAiC,EAAA;;AAE7D,IAAA,IAAM,OAAO,GAAG,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC7E,IAAA,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;AAClC,CAAC;;ACAuB,SAAA,kBAAkB,CAAC,UAAsB,EAAA;IACzD,IAAA,EAAA,GAA8B,iBAAiB,EAAE,EAAhD,YAAY,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,WAAW,GAAA,EAAA,CAAA,CAAA,CAAuB,CAAC;IAExD,SAAS,iBAAiB,CAAC,EAA8D,EAAA;AAA7D,QAAA,IAAA,MAAM,YAAA,EAAE,SAAS,GAAA,EAAA,CAAA,SAAA,EAAE,OAAO,GAAA,EAAA,CAAA,OAAA,CAAA;QACpD,IAAI,WAAW,EAAE;AACf,YAAA,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;AAED,QAAA,IAAM,IAAI,GAAO,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,UAAU,CAAK,EAAA,MAAM,CAAC,CAAC;QAGtC,IAAA,SAAS,GAkBP,IAAI,CAlBG,SAAA,EACT,SAAS,GAiBP,IAAI,CAAA,SAjBG,EACT,QAAQ,GAgBN,IAAI,CAhBE,QAAA,EACR,KAAK,GAeH,IAAI,CAAA,KAfD,EACL,KAAK,GAcH,IAAI,CAdD,KAAA,EACL,MAAM,GAaJ,IAAI,CAAA,MAbA,EACN,SAAS,GAYP,IAAI,CAAA,SAZG,EACT,EAAA,GAWE,IAAI,CAAA,QAXO,EAAb,QAAQ,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,EAAE,GAAA,EAAA,EACb,EAUE,GAAA,IAAI,CAVU,QAAA,EAAhB,QAAQ,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,KAAK,GAAA,EAAA,EAChB,QAAQ,GASN,IAAI,CATE,QAAA,EACR,EAQE,GAAA,IAAI,MARI,EAAV,KAAK,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,EAAE,GAAA,EAAA,EACV,EAOE,GAAA,IAAI,CAPG,IAAA,EAAT,IAAI,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAA,EAAA,EACT,EAAA,GAME,IAAI,CAAA,QANO,EAAb,QAAQ,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,EAAE,GAAA,EAAA,EACb,EAKE,GAAA,IAAI,CALS,UAAA,EAAf,UAAU,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAA,EAAA,EACf,EAAA,GAIE,IAAI,CAAA,kBAJgB,EAAtB,kBAAkB,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,CAAC,GAAA,EAAA,EACtB,EAGE,GAAA,IAAI,CAHY,MAAA,EAAlB,MAAM,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,SAAS,GAAA,EAAA,EAClB,KAAK,GAEH,IAAI,CAFD,KAAA,EACL,UAAU,GACR,IAAI,CAAA,UADI,CACH;QAET,IAAI,YAAY,EAAE;AAChB,YAAA,IAAM,YAAY,GAAwB;AACxC,gBAAA,QAAQ,EAAE,SAAS,GAAG,SAAS,GAAG,YAAA,EAAM,OAAA,IAAI,GAAA;AAC5C,gBAAA,OAAO,EAAE,OAAO,GAAG,OAAO,GAAG,YAAA,EAAM,OAAA,IAAI,GAAA;AACvC,gBAAA,GAAG,EAAE,SAAS;AACd,gBAAA,GAAG,EAAE,SAAS;AACd,gBAAA,KAAK,EAAA,KAAA;AACL,gBAAA,SAAS,EAAA,SAAA;AACT,gBAAA,QAAQ,EAAA,QAAA;AACR,gBAAA,KAAK,EAAA,KAAA;AACL,gBAAA,MAAM,EAAA,MAAA;AACN,gBAAA,QAAQ,EAAA,QAAA;AACR,gBAAA,IAAI,EAAA,IAAA;AACJ,gBAAA,QAAQ,EAAA,QAAA;AACR,gBAAA,QAAQ,EAAA,QAAA;AACR,gBAAA,UAAU,EAAA,UAAA;AACV,gBAAA,kBAAkB,EAAA,kBAAA;AAClB,gBAAA,MAAM,EAAA,MAAA;AACN,gBAAA,KAAK,EAAA,KAAA;AACL,gBAAA,QAAQ,EAAA,QAAA;AACR,gBAAA,KAAK,EAAA,KAAA;AACL,gBAAA,UAAU,EAAA,UAAA;AACV,gBAAA,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE;aACvD,CAAC;YACF,eAAe,CAAC,YAAY,CAAC,CAAC;SAC/B;KACF;AAED,IAAA,SAAS,CAAC,YAAA;QACR,IAAI,WAAW,EAAE;AACf,YAAA,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;AACH,KAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;AAElB,IAAA,OAAO,iBAAiB,CAAC;AAC3B;;AC3DM,IAAA,cAAc,GAAG,UAAC,EAOF,EAAA;AANpB,IAAA,IAAA,IAAI,GAAA,EAAA,CAAA,IAAA,EACJ,SAAS,GAAA,EAAA,CAAA,SAAA,EACT,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,SAAS,GAAA,EAAA,CAAA,SAAA,EACT,OAAO,GAAA,EAAA,CAAA,OAAA,EACJ,MAAM,GAAA,MAAA,CAAA,EAAA,EANa,yDAOvB,CADU,CAAA;AAET,IAAA,IAAM,iBAAiB,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAErD,IAAA,QACE,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA,EACE,SAAS,EAAE,SAAS,EACpB,OAAO,EAAE,YAAY,EAAA,OAAA,iBAAiB,CAAC,EAAC,MAAM,EAAA,MAAA,EAAE,SAAS,EAAA,SAAA,EAAE,OAAO,EAAA,OAAA,EAAC,CAAC,CAAA,EAAA,EAAA,EAEnE,IAAI,IAAI,QAAQ,CACV,EACT;AACJ;;ACpBA,IAAM,eAAe,GAAG,aAAa,CAAmB;AACtD,IAAA,MAAM,EAAE,EAAmB;AAC3B,IAAA,iBAAiB,EAAE,YAAA,EAAM,OAAA,IAAI,GAAA;AAC7B,IAAA,SAAS,EAAE,YAAA,EAAM,OAAA,IAAI,GAAA;AACrB,IAAA,OAAO,EAAE,YAAA,EAAM,OAAA,IAAI,GAAA;AACpB,CAAA,CAAC;;ACJF,IAAM,gBAAgB,GAAG,UAAC,EAKF,EAAA;AAJtB,IAAA,IAAA,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,SAAS,GAAA,EAAA,CAAA,SAAA,EACT,OAAO,GAAA,EAAA,CAAA,OAAA,EACJ,MAAM,GAJe,MAAA,CAAA,EAAA,EAAA,CAAA,UAAA,EAAA,WAAA,EAAA,SAAA,CAKzB,CADU,CAAA;AAET,IAAA,IAAM,iBAAiB,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAErD,QACE,KAAC,CAAA,aAAA,CAAA,eAAe,CAAC,QAAQ,IAAC,KAAK,EAAE,EAAC,MAAM,EAAA,MAAA,EAAE,iBAAiB,EAAA,iBAAA,EAAE,SAAS,EAAA,SAAA,EAAE,OAAO,EAAA,OAAA,EAAC,EAC7E,EAAA,QAAQ,CACgB,EAC3B;AACJ,CAAC;;ACbD,IAAM,qBAAqB,GAAG,UAAC,EAM9B,EAAA;QALC,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,GAAG,GAAA,EAAA,CAAA,GAAA,CAAA;AAKG,IAAA,IAAA,KAAkD,UAAU,CAAC,eAAe,CAAC,EAA5E,MAAM,GAAA,EAAA,CAAA,MAAA,EAAE,iBAAiB,uBAAA,EAAE,SAAS,eAAA,EAAE,OAAO,aAA+B,CAAC;AAEpF,IAAA,IAAM,yBAAyB,GAAG,YAAA,EAAY,OAAA,iBAAiB,CAAC,EAAC,MAAM,EAAA,MAAA,EAAE,SAAS,EAAA,SAAA,EAAE,OAAO,EAAA,OAAA,EAAC,CAAC,CAAA,EAAA,CAAC;IAC9F,OAAO,QAAQ,CAAC,EAAC,iBAAiB,EAAE,yBAAyB,EAAE,GAAG,EAAA,GAAA,EAAC,CAAC,CAAC;AACvE,CAAC,CAAC;AAEF;AACA,IAAM,gBAAgB,GAAG,UAAU,CACjC,UACE,EAAwF,EACxF,GAAQ,EAAA;AADP,IAAA,IAAA,QAAQ,GAAA,EAAA,CAAA,QAAA,EAAa,WAAW,GAAA,EAAA,CAAA,SAAA,EAAW,SAAS,GAAA,EAAA,CAAA,OAAA,EAAK,MAAM,GAAhE,MAAA,CAAA,EAAA,EAAA,CAAA,UAAA,EAAA,WAAA,EAAA,SAAA,CAAiE,CAAD,CAAA;AAGhE,IAAA,IAAM,SAAS,GAAG,WAAW,GAAG,WAAW,GAAG,YAAW,EAAA,OAAA,IAAI,CAAA,EAAA,CAAC;AAC9D,IAAA,IAAM,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG,YAAW,EAAA,OAAA,IAAI,CAAA,EAAA,CAAC;AACxD,IAAA,QACE,KAAA,CAAA,aAAA,CAAC,gBAAgB,EAAA,QAAA,CAAA,EAAA,EAAK,MAAM,EAAA,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAA,CAAA;QAClE,KAAC,CAAA,aAAA,CAAA,qBAAqB,EAAC,EAAA,GAAG,EAAE,GAAG,IAAG,QAAQ,CAAyB,CAClD,EACnB;AACJ,CAAC;;;;"}