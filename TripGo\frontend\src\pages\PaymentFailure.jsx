import React from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { motion } from "framer-motion";
import { XCircle, RefreshCw } from "lucide-react";

const PaymentFailure = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const reference = searchParams.get("reference");
  const message = searchParams.get("message") || "Payment was not completed";

  const handleRetry = () => {
    navigate("/tours");
  };

  const handleViewBookings = () => {
    navigate("/my-bookings");
  };

  return (
    <div className="min-h-screen bg-gradient-to-r from-sky-50 via-blue-50 to-violet-50 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-md mx-auto p-8 bg-white rounded-lg shadow-lg text-center"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        >
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
        </motion.div>
        
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">
          Payment Failed
        </h2>
        
        <p className="text-gray-600 mb-6">
          {message}. Don't worry, you can try again or contact our support team for assistance.
        </p>

        {reference && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-600">
              <strong>Reference:</strong> {reference}
            </p>
          </div>
        )}

        <div className="space-y-3">
          <button
            onClick={handleRetry}
            className="w-full bg-gradient-to-b from-sky-500 to-blue-500 text-white hover:from-sky-800 hover:to-blue-700 px-6 py-3 rounded-lg transition duration-300 flex items-center justify-center"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </button>
          <button
            onClick={handleViewBookings}
            className="w-full bg-gray-500 text-white hover:bg-gray-600 px-6 py-3 rounded-lg transition duration-300"
          >
            View My Bookings
          </button>
          <button
            onClick={() => navigate("/")}
            className="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg transition duration-300"
          >
            Go Home
          </button>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team at{" "}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default PaymentFailure;
