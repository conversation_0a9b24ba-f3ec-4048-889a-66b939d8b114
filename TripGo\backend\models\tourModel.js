import mongoose from "mongoose";

const tourSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  city: {
    type: String,
    required: true,
    trim: true,
  },
  address: {
    type: String,
    trim: true,
  },
  distance: {
    type: Number,
    required: true,
    min: 0,
  },
  price: {
    type: Number,
    required: true,
    min: 0,
  },
  maxGroupSize: {
    type: Number,
    required: true,
    min: 1,
  },
  desc: {
    type: String,
    required: true,
  },
  availableDates: [{
    type: String,
    required: true,
  }],
  photo: {
    type: String,
    required: true,
  },
  featured: {
    type: Boolean,
    default: false,
  },
  avgRating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5,
  },
  reviews: [{
    name: {
      type: String,
      required: true,
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
    },
    comment: {
      type: String,
    },
    date: {
      type: Date,
      default: Date.now,
    },
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Update the updatedAt field before saving
tourSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

const tourModel = mongoose.models.tour || mongoose.model("tour", tourSchema);

export default tourModel;
