import React, { useState, useEffect, useContext } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { AppContext } from "../../context/AppContext";
import AdminLayout from "../../components/admin/AdminLayout";
import { Save, ArrowLeft, Plus, X } from "lucide-react";
import axios from "axios";
import { toast } from "react-toastify";

const TourForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { backendUrl, token } = useContext(AppContext);
  const isEdit = Boolean(id);

  const [formData, setFormData] = useState({
    title: "",
    city: "",
    address: "",
    distance: "",
    price: "",
    maxGroupSize: "",
    desc: "",
    photo: "",
    featured: false,
    availableDates: [""],
  });
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(isEdit);
  const [selectedFile, setSelectedFile] = useState(null);
  const [imagePreview, setImagePreview] = useState("");
  const [uploadLoading, setUploadLoading] = useState(false);

  useEffect(() => {
    if (isEdit) {
      fetchTour();
    }
  }, [id]);

  const fetchTour = async () => {
    try {
      setFetchLoading(true);
      const response = await axios.get(`${backendUrl}/api/tours/${id}`);
      
      if (response.data.success) {
        const tour = response.data.tour;
        setFormData({
          title: tour.title,
          city: tour.city,
          address: tour.address || "",
          distance: tour.distance.toString(),
          price: tour.price.toString(),
          maxGroupSize: tour.maxGroupSize.toString(),
          desc: tour.desc,
          photo: tour.photo,
          featured: tour.featured,
          availableDates: tour.availableDates.length > 0 ? tour.availableDates : [""],
        });
      }
    } catch (error) {
      console.error("Error fetching tour:", error);
      toast.error("Failed to fetch tour details");
      navigate("/admin/tours");
    } finally {
      setFetchLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleDateChange = (index, value) => {
    const newDates = [...formData.availableDates];
    newDates[index] = value;
    setFormData(prev => ({
      ...prev,
      availableDates: newDates,
    }));
  };

  const addDateField = () => {
    setFormData(prev => ({
      ...prev,
      availableDates: [...prev.availableDates, ""],
    }));
  };

  const removeDateField = (index) => {
    if (formData.availableDates.length > 1) {
      const newDates = formData.availableDates.filter((_, i) => i !== index);
      setFormData(prev => ({
        ...prev,
        availableDates: newDates,
      }));
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image size should be less than 5MB');
        return;
      }

      setSelectedFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async () => {
    if (!selectedFile) return null;

    try {
      setUploadLoading(true);
      const formData = new FormData();
      formData.append('image', selectedFile);

      const response = await axios.post(
        `${backendUrl}/api/tours/admin/upload-image`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (response.data.success) {
        return response.data.imageUrl;
      } else {
        throw new Error(response.data.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
      throw error;
    } finally {
      setUploadLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!formData.title || !formData.city || !formData.price || !formData.desc) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Check if we have either a photo URL or a selected file
    if (!formData.photo && !selectedFile) {
      toast.error("Please provide a photo URL or upload an image");
      return;
    }

    if (formData.availableDates.some(date => !date.trim())) {
      toast.error("Please fill in all available dates");
      return;
    }

    try {
      setLoading(true);

      let photoUrl = formData.photo;

      // If a file is selected, upload it first
      if (selectedFile) {
        photoUrl = await uploadImage();
      }

      const submitData = {
        ...formData,
        photo: photoUrl,
        distance: parseFloat(formData.distance),
        price: parseFloat(formData.price),
        maxGroupSize: parseInt(formData.maxGroupSize),
        availableDates: formData.availableDates.filter(date => date.trim()),
      };

      let response;
      if (isEdit) {
        response = await axios.put(
          `${backendUrl}/api/tours/admin/${id}`,
          submitData,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
      } else {
        response = await axios.post(
          `${backendUrl}/api/tours/admin`,
          submitData,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
      }

      if (response.data.success) {
        toast.success(`Tour ${isEdit ? "updated" : "created"} successfully`);
        navigate("/admin/tours");
      }
    } catch (error) {
      console.error("Error saving tour:", error);
      toast.error(error.response?.data?.message || `Failed to ${isEdit ? "update" : "create"} tour`);
    } finally {
      setLoading(false);
    }
  };

  if (fetchLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/admin/tours")}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEdit ? "Edit Tour" : "Add New Tour"}
            </h1>
            <p className="text-gray-600">
              {isEdit ? "Update tour information" : "Create a new tour package"}
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tour Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter tour title"
                  required
                />
              </div>

              {/* City */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  City *
                </label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter city"
                  required
                />
              </div>

              {/* Address */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Address
                </label>
                <input
                  type="text"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter address"
                />
              </div>

              {/* Distance */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Distance (km) *
                </label>
                <input
                  type="number"
                  name="distance"
                  value={formData.distance}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter distance"
                  min="0"
                  step="0.1"
                  required
                />
              </div>

              {/* Price */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price ($) *
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter price"
                  min="0"
                  step="0.01"
                  required
                />
              </div>

              {/* Max Group Size */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Group Size *
                </label>
                <input
                  type="number"
                  name="maxGroupSize"
                  value={formData.maxGroupSize}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter max group size"
                  min="1"
                  required
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                name="desc"
                value={formData.desc}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter tour description"
                required
              />
            </div>

            {/* Photo Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tour Photo *
              </label>

              {/* File Upload */}
              <div className="mb-4">
                <label className="block text-sm text-gray-600 mb-2">
                  Upload Image
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Supported formats: JPG, PNG, GIF. Max size: 5MB
                </p>
              </div>

              {/* Image Preview */}
              {imagePreview && (
                <div className="mb-4">
                  <label className="block text-sm text-gray-600 mb-2">
                    Preview
                  </label>
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="w-32 h-32 object-cover rounded-lg border"
                  />
                </div>
              )}

              {/* OR Divider */}
              <div className="flex items-center my-4">
                <div className="flex-1 border-t border-gray-300"></div>
                <span className="px-3 text-sm text-gray-500">OR</span>
                <div className="flex-1 border-t border-gray-300"></div>
              </div>

              {/* URL Input */}
              <div>
                <label className="block text-sm text-gray-600 mb-2">
                  Photo URL
                </label>
                <input
                  type="url"
                  name="photo"
                  value={formData.photo}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter photo URL"
                />
              </div>

              {/* Current Photo Preview for Edit Mode */}
              {isEdit && formData.photo && !imagePreview && (
                <div className="mt-4">
                  <label className="block text-sm text-gray-600 mb-2">
                    Current Photo
                  </label>
                  <img
                    src={formData.photo.startsWith('http') ? formData.photo : `${backendUrl}${formData.photo}`}
                    alt="Current tour"
                    className="w-32 h-32 object-cover rounded-lg border"
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                </div>
              )}
            </div>

            {/* Available Dates */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Available Dates *
              </label>
              <div className="space-y-2">
                {formData.availableDates.map((date, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="date"
                      value={date}
                      onChange={(e) => handleDateChange(index, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                    {formData.availableDates.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeDateField(index)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addDateField}
                  className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
                >
                  <Plus size={16} />
                  <span>Add Date</span>
                </button>
              </div>
            </div>

            {/* Featured */}
            <div className="flex items-center">
              <input
                type="checkbox"
                name="featured"
                checked={formData.featured}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Featured Tour
              </label>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => navigate("/admin/tours")}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || uploadLoading}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
              >
                {(loading || uploadLoading) ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Save size={16} />
                )}
                <span>
                  {uploadLoading ? "Uploading..." : loading ? "Saving..." : "Save Tour"}
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
};

export default TourForm;
