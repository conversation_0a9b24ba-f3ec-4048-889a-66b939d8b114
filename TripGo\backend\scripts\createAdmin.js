import mongoose from "mongoose";
import bcrypt from "bcryptjs";
import userModel from "../models/userModel.js";
import "dotenv/config";

const createAdmin = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(`${process.env.MONGODB_URL}/TripGo`);
    console.log("Connected to MongoDB");

    // Check if admin already exists
    const existingAdmin = await userModel.findOne({ role: "admin" });
    if (existingAdmin) {
      console.log("Admin user already exists:", existingAdmin.email);
      process.exit(0);
    }

    // Create admin user
    const adminData = {
      name: "Admin",
      email: "<EMAIL>",
      password: "admin123", // Change this to a secure password
      role: "admin",
    };

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(adminData.password, salt);

    const admin = new userModel({
      ...adminData,
      password: hashedPassword,
    });

    await admin.save();
    console.log("Admin user created successfully!");
    console.log("Email: <EMAIL>");
    console.log("Password: admin123");
    console.log("Please change the password after first login.");

  } catch (error) {
    console.error("Error creating admin user:", error);
  } finally {
    mongoose.connection.close();
  }
};

createAdmin();
