export const getImageUrl = (photo, backendUrl) => {
  if (!photo) return '/tour.jpg'; // fallback image
  
  // If it's already a full URL (starts with http/https), return as is
  if (photo.startsWith('http://') || photo.startsWith('https://')) {
    return photo;
  }
  
  // If it's a relative path (uploaded image), prepend backend URL
  if (photo.startsWith('/uploads/') || photo.startsWith('uploads/')) {
    return `${backendUrl}${photo.startsWith('/') ? '' : '/'}${photo}`;
  }
  
  // For any other case, assume it's a relative path
  return `${backendUrl}/${photo}`;
};
