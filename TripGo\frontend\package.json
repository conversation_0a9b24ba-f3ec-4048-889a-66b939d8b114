{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-pdf/renderer": "^4.1.5", "axios": "^1.7.9", "framer-motion": "^11.15.0", "lucide-react": "^0.468.0", "mongodb": "^6.12.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-paystack": "^5.0.0", "react-router-dom": "^7.0.2", "react-toastify": "^10.0.6"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "vite": "^6.0.1"}}