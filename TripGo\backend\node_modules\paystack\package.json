{"name": "paystack", "version": "2.0.1", "description": "Paystack API wrapper", "main": "index.js", "scripts": {"test": "./node_modules/.bin/mocha ./test/*.js --reporter spec --timeout 8000"}, "keywords": ["payment", "API", "naira", "paystack"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://obem.be/opeyemi)", "license": "MIT", "dependencies": {"promise": "^7.1.1", "request": "^2.79.0"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.2.0"}, "repository": {"type": "git", "url": "https://github.com/kehers/paystack.git"}, "bugs": {"url": "https://github.com/kehers/paystack/issues"}, "homepage": "https://github.com/kehers/paystack"}