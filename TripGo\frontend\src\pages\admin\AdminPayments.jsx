import React, { useState, useEffect, useContext } from "react";
import { AppContext } from "../../context/AppContext";
import AdminLayout from "../../components/admin/AdminLayout";
import { 
  CreditCard, 
  CheckCircle, 
  XCircle, 
  Clock, 
  User, 
  MapPin,
  Calendar,
  DollarSign,
  Eye
} from "lucide-react";
import axios from "axios";
import { toast } from "react-toastify";
import { motion } from "framer-motion";

const AdminPayments = () => {
  const { backendUrl, token } = useContext(AppContext);
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [confirmingBooking, setConfirmingBooking] = useState(null);
  const [migrating, setMigrating] = useState(false);

  useEffect(() => {
    fetchBookingsAwaitingConfirmation();
  }, []);

  const fetchBookingsAwaitingConfirmation = async () => {
    try {
      setLoading(true);
      console.log("Fetching bookings awaiting confirmation...");
      console.log("Backend URL:", backendUrl);
      console.log("Token:", token ? "Present" : "Missing");

      const response = await axios.get(
        `${backendUrl}/api/payments/admin/awaiting-confirmation`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      console.log("Response:", response.data);

      if (response.data.success) {
        console.log("Bookings found:", response.data.bookings.length);
        setBookings(response.data.bookings);
      } else {
        console.error("API returned success: false", response.data);
        toast.error("Failed to fetch bookings");
      }
    } catch (error) {
      console.error("Error fetching bookings:", error);
      console.error("Error details:", error.response?.data);
      toast.error("Failed to fetch bookings");
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmPayment = async (bookingId, action) => {
    try {
      setConfirmingBooking(bookingId);
      const response = await axios.put(
        `${backendUrl}/api/payments/admin/confirm/${bookingId}`,
        { action },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        toast.success(
          `Booking ${action === 'confirm' ? 'confirmed' : 'rejected'} successfully`
        );
        // Remove the booking from the list
        setBookings(bookings.filter(booking => booking._id !== bookingId));
      } else {
        toast.error("Failed to update booking");
      }
    } catch (error) {
      console.error("Error updating booking:", error);
      toast.error("Failed to update booking");
    } finally {
      setConfirmingBooking(null);
    }
  };

  const handleMigrateBookings = async () => {
    try {
      setMigrating(true);
      const response = await axios.post(
        `${backendUrl}/api/payments/admin/migrate-bookings`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        toast.success(response.data.message);
        // Refresh the bookings list
        fetchBookingsAwaitingConfirmation();
      } else {
        toast.error("Failed to migrate bookings");
      }
    } catch (error) {
      console.error("Error migrating bookings:", error);
      toast.error("Failed to migrate bookings");
    } finally {
      setMigrating(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCurrency = (amount) => {
    return `GH₵${amount.toLocaleString()}`;
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Payment Confirmations</h1>
            <p className="text-gray-600">
              Review and confirm payments received from customers
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={handleMigrateBookings}
              disabled={migrating}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {migrating ? 'Migrating...' : 'Migrate Old Bookings'}
            </button>
            <div className="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg">
              <Clock className="h-5 w-5 text-blue-600" />
              <span className="text-blue-600 font-medium">
                {bookings.length} awaiting confirmation
              </span>
            </div>
          </div>
        </div>

        {/* Bookings List */}
        {bookings.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg shadow">
            <CreditCard size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No payments awaiting confirmation
            </h3>
            <p className="text-gray-500">
              All payments have been processed and confirmed.
            </p>
          </div>
        ) : (
          <div className="grid gap-6">
            {bookings.map((booking, index) => (
              <motion.div
                key={booking._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {booking.tourTitle}
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center text-gray-600">
                          <User className="mr-2" size={16} />
                          <div>
                            <p className="font-medium">{booking.name}</p>
                            <p className="text-xs">{booking.email}</p>
                          </div>
                        </div>
                        <div className="flex items-center text-gray-600">
                          <DollarSign className="mr-2" size={16} />
                          <div>
                            <p className="font-medium">{formatCurrency(booking.totalPrice)}</p>
                            <p className="text-xs">{booking.travelers} traveler(s)</p>
                          </div>
                        </div>
                        <div className="flex items-center text-gray-600">
                          <Calendar className="mr-2" size={16} />
                          <div>
                            <p className="font-medium">Paid</p>
                            <p className="text-xs">{formatDate(booking.paidAt)}</p>
                          </div>
                        </div>
                        <div className="flex items-center text-gray-600">
                          <CreditCard className="mr-2" size={16} />
                          <div>
                            <p className="font-medium">Payment Ref</p>
                            <p className="text-xs font-mono">
                              {booking.paystackReference?.slice(-8) || 'N/A'}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <Clock className="mr-1" size={14} />
                        Awaiting Confirmation
                      </span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="text-sm text-gray-500">
                      Booking ID: {booking._id.slice(-8)}
                    </div>
                    <div className="flex space-x-3">
                      <button
                        onClick={() => handleConfirmPayment(booking._id, 'reject')}
                        disabled={confirmingBooking === booking._id}
                        className="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                      >
                        <XCircle className="mr-2" size={16} />
                        {confirmingBooking === booking._id ? 'Processing...' : 'Reject'}
                      </button>
                      <button
                        onClick={() => handleConfirmPayment(booking._id, 'confirm')}
                        disabled={confirmingBooking === booking._id}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                      >
                        <CheckCircle className="mr-2" size={16} />
                        {confirmingBooking === booking._id ? 'Processing...' : 'Confirm'}
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminPayments;
