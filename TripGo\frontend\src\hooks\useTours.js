import { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { AppContext } from '../context/AppContext';

export const useTours = (options = {}) => {
  const { featured = false, limit = 0, page = 1 } = options;
  const { backendUrl } = useContext(AppContext);
  const [tours, setTours] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalTours, setTotalTours] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  const fetchTours = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      if (featured) params.append('featured', 'true');
      if (limit > 0) params.append('limit', limit.toString());
      if (page > 1) params.append('page', page.toString());

      const response = await axios.get(`${backendUrl}/api/tours?${params}`);
      
      if (response.data.success) {
        setTours(response.data.tours);
        setTotalTours(response.data.totalTours);
        setTotalPages(response.data.totalPages);
      } else {
        setError('Failed to fetch tours');
      }
    } catch (err) {
      console.error('Error fetching tours:', err);
      setError('Failed to fetch tours');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (backendUrl) {
      fetchTours();
    }
  }, [backendUrl, featured, limit, page]);

  return {
    tours,
    loading,
    error,
    totalTours,
    totalPages,
    refetch: fetchTours
  };
};

export const useTour = (id) => {
  const { backendUrl } = useContext(AppContext);
  const [tour, setTour] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTour = async () => {
      if (!id || !backendUrl) return;
      
      try {
        setLoading(true);
        setError(null);
        
        const response = await axios.get(`${backendUrl}/api/tours/${id}`);
        
        if (response.data.success) {
          setTour(response.data.tour);
        } else {
          setError('Tour not found');
        }
      } catch (err) {
        console.error('Error fetching tour:', err);
        setError('Failed to fetch tour details');
      } finally {
        setLoading(false);
      }
    };

    fetchTour();
  }, [id, backendUrl]);

  return { tour, loading, error };
};
