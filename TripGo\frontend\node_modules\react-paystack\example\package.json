{"name": "example", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "^5.0.1"}, "scripts": {"start": "DISABLE_ESLINT_PLUGIN=true react-scripts --openssl-legacy-provider start", "build": "react-scripts --openssl-legacy-provider build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}