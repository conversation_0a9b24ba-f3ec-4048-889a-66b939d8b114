import tourModel from "../models/tourModel.js";

// Get all tours (public)
export const getAllTours = async (req, res) => {
  try {
    const { featured, limit, page = 1 } = req.query;
    
    let query = { isActive: true };
    if (featured === "true") {
      query.featured = true;
    }

    const pageSize = parseInt(limit) || 0;
    const skip = pageSize > 0 ? (parseInt(page) - 1) * pageSize : 0;

    const tours = await tourModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(pageSize);

    const totalTours = await tourModel.countDocuments(query);

    res.status(200).json({
      success: true,
      tours,
      totalTours,
      currentPage: parseInt(page),
      totalPages: pageSize > 0 ? Math.ceil(totalTours / pageSize) : 1,
    });
  } catch (error) {
    console.error("Error fetching tours:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

// Get single tour by ID (public)
export const getTourById = async (req, res) => {
  try {
    const { id } = req.params;
    const tour = await tourModel.findOne({ _id: id, isActive: true });

    if (!tour) {
      return res.status(404).json({
        success: false,
        message: "Tour not found",
      });
    }

    res.status(200).json({
      success: true,
      tour,
    });
  } catch (error) {
    console.error("Error fetching tour:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

// Create new tour (admin only)
export const createTour = async (req, res) => {
  try {
    const {
      title,
      city,
      address,
      distance,
      price,
      maxGroupSize,
      desc,
      availableDates,
      photo,
      featured,
    } = req.body;

    // Validation
    if (!title || !city || !distance || !price || !maxGroupSize || !desc || !photo) {
      return res.status(400).json({
        success: false,
        message: "All required fields must be provided",
      });
    }

    if (!availableDates || availableDates.length === 0) {
      return res.status(400).json({
        success: false,
        message: "At least one available date must be provided",
      });
    }

    const newTour = new tourModel({
      title,
      city,
      address,
      distance: parseFloat(distance),
      price: parseFloat(price),
      maxGroupSize: parseInt(maxGroupSize),
      desc,
      availableDates,
      photo,
      featured: featured || false,
    });

    const savedTour = await newTour.save();

    res.status(201).json({
      success: true,
      tour: savedTour,
      message: "Tour created successfully",
    });
  } catch (error) {
    console.error("Error creating tour:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

// Update tour (admin only)
export const updateTour = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Convert numeric fields
    if (updateData.distance) updateData.distance = parseFloat(updateData.distance);
    if (updateData.price) updateData.price = parseFloat(updateData.price);
    if (updateData.maxGroupSize) updateData.maxGroupSize = parseInt(updateData.maxGroupSize);

    const updatedTour = await tourModel.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: Date.now() },
      { new: true, runValidators: true }
    );

    if (!updatedTour) {
      return res.status(404).json({
        success: false,
        message: "Tour not found",
      });
    }

    res.status(200).json({
      success: true,
      tour: updatedTour,
      message: "Tour updated successfully",
    });
  } catch (error) {
    console.error("Error updating tour:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

// Delete tour (admin only) - soft delete
export const deleteTour = async (req, res) => {
  try {
    const { id } = req.params;

    const deletedTour = await tourModel.findByIdAndUpdate(
      id,
      { isActive: false, updatedAt: Date.now() },
      { new: true }
    );

    if (!deletedTour) {
      return res.status(404).json({
        success: false,
        message: "Tour not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Tour deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting tour:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

// Upload tour image (admin only)
export const uploadTourImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: "No image file provided",
      });
    }

    // Return the file path that can be used as the photo URL
    const imageUrl = `/uploads/tours/${req.file.filename}`;

    res.status(200).json({
      success: true,
      imageUrl: imageUrl,
      message: "Image uploaded successfully",
    });
  } catch (error) {
    console.error("Error uploading image:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

// Get all tours for admin (including inactive)
export const getAdminTours = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, status } = req.query;

    let query = {};
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: "i" } },
        { city: { $regex: search, $options: "i" } },
      ];
    }

    if (status === "active") query.isActive = true;
    if (status === "inactive") query.isActive = false;

    const pageSize = parseInt(limit);
    const skip = (parseInt(page) - 1) * pageSize;

    const tours = await tourModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(pageSize);

    const totalTours = await tourModel.countDocuments(query);

    res.status(200).json({
      success: true,
      tours,
      totalTours,
      currentPage: parseInt(page),
      totalPages: Math.ceil(totalTours / pageSize),
    });
  } catch (error) {
    console.error("Error fetching admin tours:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};
