import https from "https";
import bookingModel from "../models/bookingModel.js";

// Initialize Paystack payment
export const initializePayment = async (req, res) => {
  try {
    const { email, amount, bookingId, metadata } = req.body;

    // Validate required fields
    if (!email || !amount || !bookingId) {
      return res.status(400).json({
        success: false,
        message: "Email, amount, and booking ID are required",
      });
    }

    // Convert amount to kobo (Paystack expects amount in kobo)
    const amountInKobo = Math.round(amount * 100);

    const params = JSON.stringify({
      email,
      amount: amountInKobo,
      currency: "GHS",
      reference: `booking_${bookingId}_${Date.now()}`,
      callback_url: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/payment-success`,
      metadata: {
        bookingId,
        ...metadata,
      },
    });

    const options = {
      hostname: "api.paystack.co",
      port: 443,
      path: "/transaction/initialize",
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
    };

    const paystackReq = https.request(options, (paystackRes) => {
      let data = "";

      paystackRes.on("data", (chunk) => {
        data += chunk;
      });

      paystackRes.on("end", async () => {
        try {
          const response = JSON.parse(data);
          
          if (response.status) {
            // Update booking with payment reference
            await bookingModel.findByIdAndUpdate(bookingId, {
              paymentReference: response.data.reference,
              paymentStatus: "pending",
            });

            res.status(200).json({
              success: true,
              data: response.data,
              message: "Payment initialized successfully",
            });
          } else {
            res.status(400).json({
              success: false,
              message: response.message || "Payment initialization failed",
            });
          }
        } catch (error) {
          console.error("Error parsing Paystack response:", error);
          res.status(500).json({
            success: false,
            message: "Error processing payment initialization",
          });
        }
      });
    });

    paystackReq.on("error", (error) => {
      console.error("Paystack request error:", error);
      res.status(500).json({
        success: false,
        message: "Error connecting to payment gateway",
      });
    });

    paystackReq.write(params);
    paystackReq.end();
  } catch (error) {
    console.error("Payment initialization error:", error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

// Verify Paystack payment
export const verifyPayment = async (req, res) => {
  try {
    const { reference } = req.params;

    if (!reference) {
      return res.status(400).json({
        success: false,
        message: "Payment reference is required",
      });
    }

    const options = {
      hostname: "api.paystack.co",
      port: 443,
      path: `/transaction/verify/${reference}`,
      method: "GET",
      headers: {
        Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
      },
    };

    const paystackReq = https.request(options, (paystackRes) => {
      let data = "";

      paystackRes.on("data", (chunk) => {
        data += chunk;
      });

      paystackRes.on("end", async () => {
        try {
          const response = JSON.parse(data);
          
          if (response.status && response.data.status === "success") {
            // Payment successful - update booking
            const bookingId = response.data.metadata?.bookingId;

            if (!bookingId) {
              console.error("No booking ID found in payment metadata:", response.data.metadata);
              return res.status(400).json({
                success: false,
                message: "Booking ID not found in payment metadata",
              });
            }

            const updatedBooking = await bookingModel.findByIdAndUpdate(
              bookingId,
              {
                paymentStatus: "success",
                status: "payment_received", // Status indicating payment received but awaiting admin confirmation
                paystackReference: reference,
                paidAt: new Date(),
              },
              { new: true }
            );

            if (!updatedBooking) {
              console.error("Booking not found for ID:", bookingId);
              return res.status(404).json({
                success: false,
                message: "Booking not found",
              });
            }

            res.status(200).json({
              success: true,
              data: response.data,
              booking: updatedBooking,
              message: "Payment verified successfully",
            });
          } else {
            // Payment failed - update booking
            const bookingId = response.data?.metadata?.bookingId;
            
            if (bookingId) {
              await bookingModel.findByIdAndUpdate(bookingId, {
                paymentStatus: "failed",
                status: "cancelled",
              });
            }

            res.status(400).json({
              success: false,
              message: "Payment verification failed",
              data: response.data,
            });
          }
        } catch (error) {
          console.error("Error parsing Paystack verification response:", error);
          res.status(500).json({
            success: false,
            message: "Error processing payment verification",
          });
        }
      });
    });

    paystackReq.on("error", (error) => {
      console.error("Paystack verification request error:", error);
      res.status(500).json({
        success: false,
        message: "Error connecting to payment gateway",
      });
    });

    paystackReq.end();
  } catch (error) {
    console.error("Payment verification error:", error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

// Get payment status for a booking
export const getPaymentStatus = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const userId = req.user._id;

    const booking = await bookingModel.findOne({
      _id: bookingId,
      userId: userId,
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: "Booking not found",
      });
    }

    res.status(200).json({
      success: true,
      paymentStatus: booking.paymentStatus,
      paymentReference: booking.paymentReference,
      paystackReference: booking.paystackReference,
      paidAt: booking.paidAt,
    });
  } catch (error) {
    console.error("Get payment status error:", error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

// Admin confirm payment (admin only)
export const confirmPayment = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { action } = req.body; // 'confirm' or 'reject'

    // Validate action
    if (!action || !['confirm', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: "Action must be either 'confirm' or 'reject'",
      });
    }

    // Find the booking
    const booking = await bookingModel.findById(bookingId);

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: "Booking not found",
      });
    }

    // Check if payment was received
    if (booking.paymentStatus !== "success") {
      return res.status(400).json({
        success: false,
        message: "Cannot confirm booking without successful payment",
      });
    }

    // Update booking status based on admin action
    const newStatus = action === 'confirm' ? 'confirmed' : 'cancelled';
    const updatedBooking = await bookingModel.findByIdAndUpdate(
      bookingId,
      {
        status: newStatus,
        confirmedAt: action === 'confirm' ? new Date() : null,
        confirmedBy: req.user._id,
      },
      { new: true }
    ).populate('userId', 'name email');

    res.status(200).json({
      success: true,
      booking: updatedBooking,
      message: `Booking ${action === 'confirm' ? 'confirmed' : 'rejected'} successfully`,
    });
  } catch (error) {
    console.error("Confirm payment error:", error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

// Get bookings awaiting confirmation (admin only)
export const getBookingsAwaitingConfirmation = async (req, res) => {
  try {
    console.log("Fetching bookings awaiting confirmation...");

    // Debug: Let's see all bookings and their statuses
    const allBookings = await bookingModel.find({}).select('status paymentStatus tourTitle paidAt createdAt');
    console.log("All bookings in database:", allBookings.map(b => ({
      id: b._id,
      status: b.status,
      paymentStatus: b.paymentStatus,
      tourTitle: b.tourTitle,
      paidAt: b.paidAt,
      createdAt: b.createdAt
    })));

    // First, let's see all bookings with payment_received status
    const allPaymentReceived = await bookingModel.find({ status: "payment_received" });
    console.log("All bookings with payment_received status:", allPaymentReceived.length);

    // Then, let's see all bookings with success payment status
    const allSuccessPayments = await bookingModel.find({ paymentStatus: "success" });
    console.log("All bookings with success payment status:", allSuccessPayments.length);

    // For now, let's also include bookings that were confirmed before our change
    // These would be bookings with paymentStatus: "success" but status: "confirmed"
    const bookings = await bookingModel
      .find({
        $or: [
          { status: "payment_received", paymentStatus: "success" },
          { status: "confirmed", paymentStatus: "success", confirmedAt: null } // Old bookings that need admin confirmation
        ]
      })
      .populate('userId', 'name email')
      .sort({ paidAt: -1 });

    console.log("Bookings matching criteria (including legacy):", bookings.length);
    console.log("Bookings data:", bookings.map(b => ({
      id: b._id,
      status: b.status,
      paymentStatus: b.paymentStatus,
      tourTitle: b.tourTitle,
      paidAt: b.paidAt,
      confirmedAt: b.confirmedAt
    })));

    res.status(200).json({
      success: true,
      bookings,
      count: bookings.length,
    });
  } catch (error) {
    console.error("Get bookings awaiting confirmation error:", error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

// Temporary migration function to fix existing bookings (admin only)
export const migrateExistingBookings = async (req, res) => {
  try {
    console.log("Starting migration of existing bookings...");

    // Find bookings that were paid and confirmed before our change
    const bookingsToMigrate = await bookingModel.find({
      status: "confirmed",
      paymentStatus: "success",
      confirmedAt: null // These were auto-confirmed, not manually confirmed
    });

    console.log(`Found ${bookingsToMigrate.length} bookings to migrate`);

    // Update them to payment_received status
    const updateResult = await bookingModel.updateMany(
      {
        status: "confirmed",
        paymentStatus: "success",
        confirmedAt: null
      },
      {
        status: "payment_received"
      }
    );

    console.log(`Migration completed. Updated ${updateResult.modifiedCount} bookings`);

    res.status(200).json({
      success: true,
      message: `Migration completed. Updated ${updateResult.modifiedCount} bookings from confirmed to payment_received status`,
      migratedCount: updateResult.modifiedCount,
    });
  } catch (error) {
    console.error("Migration error:", error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};
