import settingsModel from "../models/settingsModel.js";

// Get settings
export const getSettings = async (req, res) => {
  try {
    let settings = await settingsModel.findOne({});
    
    // If no settings exist, create default settings
    if (!settings) {
      settings = new settingsModel({});
      await settings.save();
    }

    res.status(200).json({ success: true, settings });
  } catch (error) {
    console.error("Error fetching settings:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

// Update settings (admin only)
export const updateSettings = async (req, res) => {
  try {
    const updateData = req.body;

    let settings = await settingsModel.findOne({});
    
    if (!settings) {
      // Create new settings if none exist
      settings = new settingsModel(updateData);
    } else {
      // Update existing settings
      Object.assign(settings, updateData);
    }

    await settings.save();

    res.status(200).json({
      success: true,
      settings,
      message: "Settings updated successfully",
    });
  } catch (error) {
    console.error("Error updating settings:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};
