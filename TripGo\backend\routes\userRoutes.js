import express from "express";
import {
  registerUser,
  loginUser,
  getAllUsers,
  updateUserRole,
  deleteUser
} from "../controllers/userController.js";
import { adminAuth } from "../middleware/auth.js";

const userRouter = express.Router();

// Public routes
userRouter.post("/register", registerUser);
userRouter.post("/login", loginUser);

// Admin routes
userRouter.get("/admin/all", adminAuth, getAllUsers);
userRouter.put("/admin/:id/role", adminAuth, updateUserRole);
userRouter.delete("/admin/:id", adminAuth, deleteUser);

export default userRouter;
