import express from "express";
import {
  initializePayment,
  verifyPayment,
  getPaymentStatus,
  confirmPayment,
  getBookingsAwaitingConfirmation,
  migrateExistingBookings,
} from "../controllers/paymentController.js";
import { verifyToken, adminAuth } from "../middleware/auth.js";

const paymentRouter = express.Router();

// Initialize payment (requires authentication)
paymentRouter.post("/initialize", verifyToken, initializePayment);

// Verify payment (public route for webhook/callback)
paymentRouter.get("/verify/:reference", verifyPayment);

// Get payment status for a booking (requires authentication)
paymentRouter.get("/status/:bookingId", verifyToken, getPaymentStatus);

// Admin routes for payment confirmation
paymentRouter.get("/admin/awaiting-confirmation", adminAuth, getBookingsAwaitingConfirmation);
paymentRouter.put("/admin/confirm/:bookingId", adminAuth, confirmPayment);
paymentRouter.post("/admin/migrate-bookings", adminAuth, migrateExistingBookings);

export default paymentRouter;
