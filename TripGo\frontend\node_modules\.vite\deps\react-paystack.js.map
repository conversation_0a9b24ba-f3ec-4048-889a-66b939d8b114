{"version": 3, "sources": ["../../react-paystack/libs/paystack-script.ts", "../../react-paystack/libs/paystack-actions.ts", "../../react-paystack/libs/use-paystack.ts", "../../react-paystack/libs/paystack-button.tsx", "../../react-paystack/libs/paystack-context.ts", "../../react-paystack/libs/paystack-provider.tsx", "../../react-paystack/libs/paystack-consumer.tsx"], "sourcesContent": ["import {useState, useEffect} from 'react';\n\nconst cachedScripts: string[] = [];\ninterface IScriptResult {\n  loaded: boolean;\n  error: boolean;\n}\n\nexport default function usePaystackScript(): boolean[] {\n  const src = 'https://js.paystack.co/v1/inline.js';\n\n  const [state, setState] = useState<IScriptResult>({\n    loaded: false,\n    error: false,\n  });\n\n  useEffect((): any => {\n    if (cachedScripts.includes(src)) {\n      setState({\n        loaded: true,\n        error: false,\n      });\n    } else {\n      cachedScripts.push(src);\n\n      const script = document.createElement('script');\n      script.src = src;\n      script.async = true;\n\n      const onScriptLoad = (): void => {\n        setState({\n          loaded: true,\n          error: false,\n        });\n      };\n\n      const onScriptError = (): void => {\n        const index = cachedScripts.indexOf(src);\n        if (index >= 0) cachedScripts.splice(index, 1);\n        script.remove();\n\n        setState({\n          loaded: true,\n          error: true,\n        });\n      };\n\n      script.addEventListener('load', onScriptLoad);\n      script.addEventListener('complete', onScriptLoad);\n      script.addEventListener('error', onScriptError);\n\n      document.body.appendChild(script);\n\n      return (): void => {\n        script.removeEventListener('load', onScriptLoad);\n        script.removeEventListener('error', onScriptError);\n      };\n    }\n  }, [src]);\n\n  return [state.loaded, state.error];\n}\n", "/* eslint-disable */\nexport let callPaystackPop = (paystackArgs: Record<string, any>): void => {\n  // @ts-ignore\n  const handler = window.PaystackPop && window.PaystackPop.setup(paystackArgs);\n  handler && handler.openIframe();\n};\n", "import {useEffect} from 'react';\nimport {HookConfig, InitializePayment} from './types';\nimport usePaystackScript from './paystack-script';\nimport {callPaystackPop} from './paystack-actions';\n\nexport default function usePaystackPayment(hookConfig: HookConfig): InitializePayment {\n  const [scriptLoaded, scriptError] = usePaystackScript();\n\n  function initializePayment({config, onSuccess, onClose}: Parameters<InitializePayment>[0]): void {\n    if (scriptError) {\n      throw new Error('Unable to load paystack inline script');\n    }\n\n    const args = {...hookConfig, ...config};\n\n    const {\n      publicKey,\n      firstname,\n      lastname,\n      phone,\n      email,\n      amount,\n      reference,\n      metadata = {},\n      currency = 'NGN',\n      channels,\n      label = '',\n      plan = '',\n      quantity = '',\n      subaccount = '',\n      transaction_charge = 0,\n      bearer = 'account',\n      split,\n      split_code,\n    } = args;\n\n    if (scriptLoaded) {\n      const paystackArgs: Record<string, any> = {\n        callback: onSuccess ? onSuccess : () => null,\n        onClose: onClose ? onClose : () => null,\n        key: publicKey,\n        ref: reference,\n        email,\n        firstname,\n        lastname,\n        phone,\n        amount,\n        currency,\n        plan,\n        quantity,\n        channels,\n        subaccount,\n        transaction_charge,\n        bearer,\n        label,\n        metadata,\n        split,\n        split_code,\n        'data-custom-button': args['data-custom-button'] || '',\n      };\n      callPaystackPop(paystackArgs);\n    }\n  }\n\n  useEffect(() => {\n    if (scriptError) {\n      throw new Error('Unable to load paystack inline script');\n    }\n  }, [scriptError]);\n\n  return initializePayment;\n}\n", "import React, {ReactNode} from 'react';\nimport usePaystackPayment from './use-paystack';\nimport {callback, PaystackProps} from './types';\n\ninterface PaystackButtonProps extends PaystackProps {\n  text?: string;\n  className?: string;\n  children?: ReactNode;\n  onSuccess?: callback;\n  onClose?: callback;\n}\n\nconst PaystackButton = ({\n  text,\n  className,\n  children,\n  onSuccess,\n  onClose,\n  ...config\n}: PaystackButtonProps): JSX.Element => {\n  const initializePayment = usePaystackPayment(config);\n\n  return (\n    <button\n      className={className}\n      onClick={(): void => initializePayment({config, onSuccess, onClose})}\n    >\n      {text || children}\n    </button>\n  );\n};\n\nexport default PaystackButton;\n", "import {createContext} from 'react';\nimport {InitializePayment, PaystackProps} from './types';\n\ntype IPaystackContext = {\n  config: PaystackProps;\n  initializePayment: InitializePayment;\n  onSuccess: () => void;\n  onClose: () => void;\n};\n\nconst PaystackContext = createContext<IPaystackContext>({\n  config: {} as PaystackProps,\n  initializePayment: () => null,\n  onSuccess: () => null,\n  onClose: () => null,\n});\n\nexport default PaystackContext;\n", "import React from 'react';\nimport PaystackContext from './paystack-context';\nimport usePaystackPayment from './use-paystack';\nimport {callback, PaystackProps} from './types';\n\ninterface PaystackProviderProps extends PaystackProps {\n  children: JSX.Element;\n  onSuccess: callback;\n  onClose: callback;\n}\n\nconst PaystackProvider = ({\n  children,\n  onSuccess,\n  onClose,\n  ...config\n}: PaystackProviderProps): JSX.Element => {\n  const initializePayment = usePaystackPayment(config);\n\n  return (\n    <PaystackContext.Provider value={{config, initializePayment, onSuccess, onClose}}>\n      {children}\n    </PaystackContext.Provider>\n  );\n};\n\nexport default PaystackProvider;\n", "import React, {forwardRef, useContext, FunctionComponentElement} from 'react';\nimport PaystackProvider from './paystack-provider';\nimport {PaystackProps} from './types';\nimport PaystackContext from './paystack-context';\n\ninterface PaystackConsumerProps extends PaystackProps {\n  children: (arg: Record<string, any>) => any;\n  onSuccess?: () => void;\n  onClose?: () => void;\n}\n\nconst PaystackConsumerChild = ({\n  children,\n  ref,\n}: {\n  children: any;\n  ref: any;\n}): FunctionComponentElement<any> => {\n  const {config, initializePayment, onSuccess, onClose} = useContext(PaystackContext);\n\n  const completeInitializePayment = (): void => initializePayment({config, onSuccess, onClose});\n  return children({initializePayment: completeInitializePayment, ref});\n};\n\n// eslint-disable-next-line react/display-name\nconst PaystackConsumer = forwardRef(\n  (\n    {children, onSuccess: paraSuccess, onClose: paraClose, ...others}: PaystackConsumerProps,\n    ref: any,\n  ): JSX.Element => {\n    const onSuccess = paraSuccess ? paraSuccess : (): any => null;\n    const onClose = paraClose ? paraClose : (): any => null;\n    return (\n      <PaystackProvider {...others} onSuccess={onSuccess} onClose={onClose}>\n        <PaystackConsumerChild ref={ref}>{children}</PaystackConsumerChild>\n      </PaystackProvider>\n    );\n  },\n);\n\nexport default PaystackConsumer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,gBAA0B,CAAA;AAMlB,SAAU,oBAAiB;AACvC,MAAM,MAAM;AAEN,MAAA,SAAoB,uBAAwB;IAChD,QAAQ;IACR,OAAO;EACR,CAAA,GAHM,QAAK,GAAA,CAAA,GAAE,WAAQ,GAAA,CAAA;AAKtB,8BAAU,WAAA;AACR,QAAI,cAAc,SAAS,GAAG,GAAG;AAC/B,eAAS;QACP,QAAQ;QACR,OAAO;MACR,CAAA;WACI;AACL,oBAAc,KAAK,GAAG;AAEtB,UAAM,WAAS,SAAS,cAAc,QAAQ;AAC9C,eAAO,MAAM;AACb,eAAO,QAAQ;AAEf,UAAM,iBAAe,WAAA;AACnB,iBAAS;UACP,QAAQ;UACR,OAAO;QACR,CAAA;MACH;AAEA,UAAM,kBAAgB,WAAA;AACpB,YAAM,QAAQ,cAAc,QAAQ,GAAG;AACvC,YAAI,SAAS;AAAG,wBAAc,OAAO,OAAO,CAAC;AAC7C,iBAAO,OAAM;AAEb,iBAAS;UACP,QAAQ;UACR,OAAO;QACR,CAAA;MACH;AAEA,eAAO,iBAAiB,QAAQ,cAAY;AAC5C,eAAO,iBAAiB,YAAY,cAAY;AAChD,eAAO,iBAAiB,SAAS,eAAa;AAE9C,eAAS,KAAK,YAAY,QAAM;AAEhC,aAAO,WAAA;AACL,iBAAO,oBAAoB,QAAQ,cAAY;AAC/C,iBAAO,oBAAoB,SAAS,eAAa;MACnD;;EAEJ,GAAG,CAAC,GAAG,CAAC;AAER,SAAO,CAAC,MAAM,QAAQ,MAAM,KAAK;AACnC;AC5DO,IAAI,kBAAkB,SAAC,cAAiC;AAE7D,MAAM,UAAU,OAAO,eAAe,OAAO,YAAY,MAAM,YAAY;AAC3E,aAAW,QAAQ,WAAU;AAC/B;ACAwB,SAAA,mBAAmB,YAAsB;AACzD,MAAA,KAA8B,kBAAiB,GAA9C,eAAY,GAAA,CAAA,GAAE,cAAW,GAAA,CAAA;AAEhC,WAAS,kBAAkBA,KAA8D;AAA7D,QAAA,SAAMA,IAAA,QAAE,YAASA,IAAA,WAAE,UAAOA,IAAA;AACpD,QAAI,aAAa;AACf,YAAM,IAAI,MAAM,uCAAuC;;AAGzD,QAAM,OAAW,SAAA,SAAA,CAAA,GAAA,UAAU,GAAK,MAAM;AAGpC,QAAA,YAkBE,KAlBO,WACT,YAiBE,KAAI,WAhBN,WAgBE,KAhBM,UACR,QAeE,KAAI,OAdN,QAcE,KAdG,OACL,SAaE,KAAI,QAZN,YAYE,KAAI,WAXN,KAWE,KAAI,UAXN,WAAW,OAAA,SAAA,CAAA,IAAE,IACb,KAUE,KAVc,UAAhB,WAAQ,OAAA,SAAG,QAAK,IAChB,WASE,KATM,UACR,KAQE,KAAI,OARN,QAAQ,OAAA,SAAA,KAAE,IACV,KAOE,KAPO,MAAT,OAAI,OAAA,SAAG,KAAE,IACT,KAME,KAAI,UANN,WAAW,OAAA,SAAA,KAAE,IACb,KAKE,KALa,YAAf,aAAU,OAAA,SAAG,KAAE,IACf,KAIE,KAAI,oBAJN,qBAAqB,OAAA,SAAA,IAAC,IACtB,KAGE,KAHgB,QAAlB,SAAM,OAAA,SAAG,YAAS,IAClB,QAEE,KAFG,OACL,aACE,KAAI;AAER,QAAI,cAAc;AAChB,UAAM,eAAoC;QACxC,UAAU,YAAY,YAAY,WAAA;AAAM,iBAAA;QAAI;QAC5C,SAAS,UAAU,UAAU,WAAA;AAAM,iBAAA;QAAI;QACvC,KAAK;QACL,KAAK;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,sBAAsB,KAAK,oBAAoB,KAAK;;AAEtD,sBAAgB,YAAY;;;AAIhC,8BAAU,WAAA;AACR,QAAI,aAAa;AACf,YAAM,IAAI,MAAM,uCAAuC;;EAE3D,GAAG,CAAC,WAAW,CAAC;AAEhB,SAAO;AACT;AC3DM,IAAA,iBAAiB,SAAC,IAOF;AANpB,MAAA,OAAI,GAAA,MACJ,YAAS,GAAA,WACT,WAAQ,GAAA,UACR,YAAS,GAAA,WACT,UAAO,GAAA,SACJ,SAAM,OAAA,IANa,CAAA,QAAA,aAAA,YAAA,aAAA,SAAA,CAOvB;AACC,MAAM,oBAAoB,mBAAmB,MAAM;AAEnD,SACE,aAAAC,QAAA,cAAA,UAAA,EACE,WACA,SAAS,WAAY;AAAA,WAAA,kBAAkB,EAAC,QAAQ,WAAW,QAAO,CAAC;EAAC,EAAA,GAEnE,QAAQ,QAAQ;AAGvB;ACpBA,IAAM,sBAAkB,4BAAgC;EACtD,QAAQ,CAAA;EACR,mBAAmB,WAAA;AAAM,WAAA;EAAI;EAC7B,WAAW,WAAA;AAAM,WAAA;EAAI;EACrB,SAAS,WAAA;AAAM,WAAA;EAAI;AACpB,CAAA;ACJD,IAAM,mBAAmB,SAAC,IAKF;AAJtB,MAAA,WAAQ,GAAA,UACR,YAAS,GAAA,WACT,UAAO,GAAA,SACJ,SAJqB,OAAA,IAAA,CAAA,YAAA,aAAA,SAAA,CAKzB;AACC,MAAM,oBAAoB,mBAAmB,MAAM;AAEnD,SACE,aAAAA,QAAC,cAAA,gBAAgB,UAAQ,EAAC,OAAO,EAAC,QAAQ,mBAAmB,WAAW,QAAO,EAAC,GAC7E,QAAQ;AAGf;ACbA,IAAM,wBAAwB,SAAC,IAM9B;MALC,WAAQ,GAAA,UACR,MAAG,GAAA;AAKG,MAAA,SAAkD,yBAAW,eAAe,GAA3E,SAAM,GAAA,QAAE,oBAAiB,GAAA,mBAAE,YAAS,GAAA,WAAE,UAAO,GAAA;AAEpD,MAAM,4BAA4B,WAAA;AAAY,WAAA,kBAAkB,EAAC,QAAQ,WAAW,QAAO,CAAC;EAAC;AAC7F,SAAO,SAAS,EAAC,mBAAmB,2BAA2B,IAAG,CAAC;AACrE;AAGA,IAAM,uBAAmB,yBACvB,SACE,IACA,KAAQ;AADP,MAAA,WAAQ,GAAA,UAAa,cAAW,GAAA,WAAW,YAAS,GAAA,SAAK,SAA1D,OAAA,IAAA,CAAA,YAAA,aAAA,SAAA,CAAiE;AAGjE,MAAM,YAAY,cAAc,cAAc,WAAW;AAAA,WAAA;EAAI;AAC7D,MAAM,UAAU,YAAY,YAAY,WAAW;AAAA,WAAA;EAAI;AACvD,SACE,aAAAA,QAAA;IAAC;IAAgB,SAAA,CAAA,GAAK,QAAM,EAAE,WAAsB,QAAgB,CAAA;IAClE,aAAAA,QAAC,cAAA,uBAAsB,EAAA,IAAQ,GAAG,QAAQ;EAAyB;AAGzE,CAAC;", "names": ["_a", "React"]}