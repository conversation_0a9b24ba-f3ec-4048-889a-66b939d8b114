import mongoose from "mongoose";

const settingsSchema = new mongoose.Schema({
  siteName: {
    type: String,
    default: "TripGo",
  },
  siteDescription: {
    type: String,
    default: "Your ultimate travel companion for discovering amazing destinations",
  },
  contactEmail: {
    type: String,
    default: "<EMAIL>",
  },
  contactPhone: {
    type: String,
    default: "+233 20 123 4567",
  },
  address: {
    type: String,
    default: "123 Independence Avenue, Accra, Ghana",
  },
  socialMedia: {
    facebook: {
      type: String,
      default: "",
    },
    twitter: {
      type: String,
      default: "",
    },
    instagram: {
      type: String,
      default: "",
    },
    linkedin: {
      type: String,
      default: "",
    },
  },
  businessSettings: {
    currency: {
      type: String,
      default: "GHS",
    },
    taxRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100,
    },
    cancellationPolicy: {
      type: String,
      default: "Free cancellation up to 24 hours before the tour starts",
    },
    refundPolicy: {
      type: String,
      default: "Full refund for cancellations made 24 hours in advance",
    },
  },
  emailSettings: {
    smtpHost: {
      type: String,
      default: "",
    },
    smtpPort: {
      type: Number,
      default: 587,
    },
    smtpUser: {
      type: String,
      default: "",
    },
    smtpPassword: {
      type: String,
      default: "",
    },
    fromEmail: {
      type: String,
      default: "<EMAIL>",
    },
  },
  maintenanceMode: {
    type: Boolean,
    default: false,
  },
  maintenanceMessage: {
    type: String,
    default: "We are currently performing maintenance. Please check back soon!",
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Update the updatedAt field before saving
settingsSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

const settingsModel = mongoose.models.settings || mongoose.model("settings", settingsSchema);

export default settingsModel;
