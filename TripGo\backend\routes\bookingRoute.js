import express from "express";
import {
  createBooking,
  getBookings,
  getAdminBookings,
} from "../controllers/bookingController.js";
import { verifyToken, adminAuth } from "../middleware/auth.js";

const bookingRouter = express.Router();

// User booking routes (require authentication)
bookingRouter.post("/", verifyToken, createBooking);
bookingRouter.get("/", verifyToken, getBookings);

// Admin booking routes (require admin authentication)
bookingRouter.get("/admin/all", adminAuth, getAdminBookings);

export default bookingRouter;
