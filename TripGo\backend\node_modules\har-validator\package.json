{"version": "5.1.5", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": "<PERSON> <<EMAIL>> (https://www.ahmadnassri.com/)", "homepage": "https://github.com/ahmadnassri/node-har-validator", "repository": {"type": "git", "url": "https://github.com/ahmadnassri/node-har-validator.git"}, "license": "MIT", "main": "lib/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=6"}, "files": ["lib"], "bugs": {"url": "https://github.com/ahmadnassri/node-har-validator/issues"}, "scripts": {"lint": "npx run-p lint:*", "test": "tap test --no-coverage", "test:coverage": "tap test --coverage-report=lcov --no-browser"}, "devDependencies": {"tap": "^14.10.8"}, "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}}