import React, { useEffect, useState, useContext } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { CheckCircle, Loader } from "lucide-react";
import { AppContext } from "../context/AppContext";
import { toast } from "react-toastify";

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { backendUrl, token } = useContext(AppContext);
  const [verifying, setVerifying] = useState(true);
  const [booking, setBooking] = useState(null);
  const [error, setError] = useState(null);

  const reference = searchParams.get("reference");

  useEffect(() => {
    if (!reference) {
      setError("No payment reference found");
      setVerifying(false);
      return;
    }

    verifyPayment();
  }, [reference]);

  const verifyPayment = async () => {
    try {
      const response = await fetch(`${backendUrl}/api/payments/verify/${reference}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        setBooking(data.booking);
        toast.success("Payment verified successfully!");
      } else {
        setError("Payment verification failed");
        toast.error("Payment verification failed");
      }
    } catch (error) {
      console.error("Payment verification error:", error);
      setError("Error verifying payment");
      toast.error("Error verifying payment");
    } finally {
      setVerifying(false);
    }
  };

  const handleViewInvoice = () => {
    if (booking) {
      navigate("/invoice", { state: { booking } });
    }
  };

  const handleViewBookings = () => {
    navigate("/my-bookings");
  };

  if (verifying) {
    return (
      <div className="min-h-screen bg-gradient-to-r from-sky-50 via-blue-50 to-violet-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <Loader className="animate-spin h-16 w-16 text-blue-600 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">
            Verifying Payment...
          </h2>
          <p className="text-gray-600">Please wait while we confirm your payment</p>
        </motion.div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-r from-sky-50 via-blue-50 to-violet-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="max-w-md mx-auto p-8 bg-white rounded-lg shadow-lg text-center"
        >
          <div className="text-red-500 mb-4">
            <svg className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Payment Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="space-y-3">
            <button
              onClick={() => navigate("/tours")}
              className="w-full bg-gradient-to-b from-sky-500 to-blue-500 text-white hover:from-sky-800 hover:to-blue-700 px-6 py-3 rounded-lg transition duration-300"
            >
              Browse Tours
            </button>
            <button
              onClick={() => navigate("/my-bookings")}
              className="w-full bg-gray-500 text-white hover:bg-gray-600 px-6 py-3 rounded-lg transition duration-300"
            >
              View My Bookings
            </button>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-r from-sky-50 via-blue-50 to-violet-50 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-md mx-auto p-8 bg-white rounded-lg shadow-lg text-center"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        >
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
        </motion.div>
        
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">
          Payment Successful!
        </h2>
        
        <p className="text-gray-600 mb-6">
          Your booking has been confirmed and payment processed successfully.
        </p>

        {booking && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-semibold text-gray-800 mb-2">Booking Details:</h3>
            <p className="text-sm text-gray-600">Tour: {booking.tourTitle}</p>
            <p className="text-sm text-gray-600">Amount: GH₵{booking.totalPrice}</p>
            <p className="text-sm text-gray-600">Travelers: {booking.travelers}</p>
            <p className="text-sm text-gray-600">Status: {booking.status}</p>
          </div>
        )}

        <div className="space-y-3">
          <button
            onClick={handleViewInvoice}
            className="w-full bg-gradient-to-b from-sky-500 to-blue-500 text-white hover:from-sky-800 hover:to-blue-700 px-6 py-3 rounded-lg transition duration-300"
          >
            View Invoice
          </button>
          <button
            onClick={handleViewBookings}
            className="w-full bg-gray-500 text-white hover:bg-gray-600 px-6 py-3 rounded-lg transition duration-300"
          >
            View All Bookings
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default PaymentSuccess;
