import React, { useState, useEffect, useContext } from "react";
import { AppContext } from "../context/AppContext";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Calendar, MapPin, Users, DollarSign } from "lucide-react";
import axios from "axios";
import { toast } from "react-toastify";

const MyBookings = () => {
  const { user, token, backendUrl } = useContext(AppContext);
  const navigate = useNavigate();
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);

  // Redirect if not authenticated
  useEffect(() => {
    if (!user || !token) {
      navigate("/login");
      return;
    }
    fetchBookings();
  }, [user, token, navigate]);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${backendUrl}/api/bookings`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data.success) {
        setBookings(response.data.bookings);
      } else {
        toast.error("Failed to fetch bookings");
      }
    } catch (error) {
      console.error("Error fetching bookings:", error);
      toast.error("Failed to fetch bookings");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-r from-sky-50 via-blue-50 to-violet-50 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-r from-sky-50 via-blue-50 to-violet-50 py-8">
      <div className="max-w-6xl mx-auto p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-8">My Bookings</h1>
          
          {bookings.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600 text-lg mb-4">You haven't made any bookings yet.</p>
              <button
                onClick={() => navigate("/tours")}
                className="bg-gradient-to-b from-sky-500 to-blue-500 text-white hover:from-sky-800 hover:to-blue-700 px-6 py-3 rounded-lg transition duration-300"
              >
                Browse Tours
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {bookings.map((booking, index) => (
                <motion.div
                  key={booking._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/20 rounded-lg shadow-lg p-6 hover:shadow-xl transition duration-300"
                >
                  <div className="mb-4">
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      {booking.tourTitle}
                    </h3>
                    <div className="flex items-center gap-2 mb-2">
                      <span
                        className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                          booking.status === "confirmed"
                            ? "bg-green-100 text-green-800"
                            : booking.status === "payment_received"
                            ? "bg-blue-100 text-blue-800"
                            : booking.status === "cancelled"
                            ? "bg-red-100 text-red-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {booking.status === "confirmed"
                          ? "Confirmed"
                          : booking.status === "payment_received"
                          ? "Awaiting Confirmation"
                          : booking.status === "cancelled"
                          ? "Cancelled"
                          : "Pending"}
                      </span>
                      <span
                        className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                          booking.paymentStatus === "success"
                            ? "bg-green-100 text-green-800"
                            : booking.paymentStatus === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {booking.paymentStatus === "success"
                          ? "Paid"
                          : booking.paymentStatus === "pending"
                          ? "Payment Pending"
                          : "Payment Failed"}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center text-gray-600">
                      <Users className="mr-2" size={16} />
                      <span>{booking.travelers} traveler(s)</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <DollarSign className="mr-2" size={16} />
                      <span>GH₵{booking.totalPrice}</span>
                    </div>

                    {booking.paidAt && (
                      <div className="flex items-center text-gray-600">
                        <span className="mr-2">Paid on:</span>
                        <span className="text-sm">{formatDate(booking.paidAt)}</span>
                      </div>
                    )}
                    <div className="flex items-center text-gray-600">
                      <span className="mr-2">Booking ID:</span>
                      <span className="text-sm font-mono">{booking._id.slice(-8)}</span>
                    </div>
                    {booking.paystackReference && (
                      <div className="flex items-center text-gray-600">
                        <span className="mr-2">Payment Ref:</span>
                        <span className="text-sm font-mono">{booking.paystackReference.slice(-8)}</span>
                      </div>
                    )}
                    <div className="flex items-center text-gray-600">
                      <Calendar className="mr-2" size={16} />
                      <span>Booked on {formatDate(booking.createdAt)}</span>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <p className="text-sm text-gray-600 mb-1">
                      <strong>Name:</strong> {booking.name}
                    </p>
                    <p className="text-sm text-gray-600 mb-1">
                      <strong>Email:</strong> {booking.email}
                    </p>
                    <p className="text-sm text-gray-600 mb-1">
                      <strong>Phone:</strong> {booking.phone}
                    </p>
                    {booking.specialRequests && (
                      <p className="text-sm text-gray-600">
                        <strong>Special Requests:</strong> {booking.specialRequests}
                      </p>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default MyBookings;
