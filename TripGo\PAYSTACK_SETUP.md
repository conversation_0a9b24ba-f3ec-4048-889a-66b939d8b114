# Paystack Payment Integration Setup Guide

This guide will help you set up Paystack payment integration for the TripGo booking system.

## Prerequisites

1. A Paystack account (sign up at https://paystack.com)
2. Paystack API keys (test and live)

## Setup Instructions

### 1. Get Paystack API Keys

1. Log in to your Paystack Dashboard
2. Go to Settings > API Keys & Webhooks
3. Copy your **Public Key** and **Secret Key**
4. For testing, use the test keys (they start with `pk_test_` and `sk_test_`)

### 2. Configure Backend Environment Variables

Update `TripGo/backend/.env` with your Paystack keys:

```env
# Paystack Configuration
PAYSTACK_SECRET_KEY=sk_test_your_actual_secret_key_here
PAYSTACK_PUBLIC_KEY=pk_test_your_actual_public_key_here

# Frontend URL (for payment callbacks)
FRONTEND_URL=http://localhost:5173
```

### 3. Configure Frontend Environment Variables

Update `TripGo/frontend/.env` with your Paystack public key:

```env
VITE_BACKEND_URL=http://localhost:4000
VITE_PAYSTACK_PUBLIC_KEY=pk_test_your_actual_public_key_here
```

### 4. Install Dependencies

Backend dependencies are already installed. For frontend:

```bash
cd TripGo/frontend
npm install react-paystack
```

## How the Payment Flow Works

### 1. Booking Creation
- User fills out booking form
- System creates a booking with `status: "pending"` and `paymentStatus: "pending"`
- Paystack payment popup is triggered

### 2. Payment Processing
- User completes payment through Paystack
- Paystack redirects to success/failure page
- System verifies payment with Paystack API

### 3. Payment Verification
- Backend verifies payment using Paystack's verification endpoint
- If successful: booking status becomes `"confirmed"` and payment status becomes `"success"`
- If failed: booking status becomes `"cancelled"` and payment status becomes `"failed"`

### 4. Invoice Generation
- User is redirected to invoice page with payment details
- Invoice shows payment status and reference number

## Testing the Integration

### Test Card Numbers (Paystack Test Mode)

Use these test card numbers for testing:

**Successful Payment:**
- Card Number: `****************`
- Expiry: Any future date
- CVV: Any 3 digits

**Failed Payment:**
- Card Number: `****************`
- Expiry: Any future date
- CVV: Any 3 digits

### Test Flow

1. Start both backend and frontend servers
2. Navigate to a tour and click "Book Now"
3. Fill out the booking form
4. Click "Pay Now with Paystack"
5. Use test card numbers above
6. Verify the payment flow and booking status

## Production Deployment

### 1. Switch to Live Keys
Replace test keys with live keys in environment variables:
- `pk_live_...` for public key
- `sk_live_...` for secret key

### 2. Update URLs
Update `FRONTEND_URL` in backend `.env` to your production domain:
```env
FRONTEND_URL=https://yourdomain.com
```

### 3. Webhook Setup (Optional)
For additional security, set up webhooks in Paystack Dashboard:
- Webhook URL: `https://yourdomain.com/api/payments/webhook`
- Events: `charge.success`, `charge.failed`

## Security Notes

1. **Never expose secret keys** in frontend code
2. **Always verify payments** on the backend
3. **Use HTTPS** in production
4. **Validate payment amounts** before processing
5. **Log all payment transactions** for audit purposes

## Troubleshooting

### Common Issues

1. **Payment popup doesn't appear**
   - Check if Paystack public key is correctly set
   - Verify react-paystack is properly installed

2. **Payment verification fails**
   - Check if Paystack secret key is correct
   - Ensure backend can reach Paystack API

3. **Booking status not updating**
   - Check payment verification endpoint
   - Verify database connection

### Debug Mode

Enable debug logging by adding to backend:
```javascript
console.log('Payment verification response:', response);
```

## Support

For Paystack-specific issues, refer to:
- [Paystack Documentation](https://paystack.com/docs)
- [Paystack Support](https://paystack.com/support)

For TripGo integration issues, check the application logs and ensure all environment variables are properly set.
